'use client';
import { useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './style.module.scss';
import TestimonialCarousel from './components/TestimonialCarousel';

import Hero3 from '@/components/Heros/Hero3';
import AnimatedLink from '@/components/AnimatedLink';
import { motion } from 'framer-motion'



export default function Testimonials({ locale = 'fr' }) {
  const { t } = useTranslation('pages');

  // Récupérer les données de témoignages depuis les traductions
  const testimonialsData = useMemo(() => {
    const testimonials = [];
    let index = 0;

    // Boucle pour récupérer tous les témoignages disponibles
    while (true) {
      const name = t(`agency.testimonials.testimonials_data.${index}.name`);
      const content = t(`agency.testimonials.testimonials_data.${index}.content`);

      // Si la traduction retourne la clé au lieu de la valeur, on arrête
      if (name === `agency.testimonials.testimonials_data.${index}.name` ||
          content === `agency.testimonials.testimonials_data.${index}.content`) {
        break;
      }

      testimonials.push({ name, content });
      index++;
    }

    return testimonials;
  }, [t]);



  // Construire la description avec AnimatedLink
  const descriptionTemplate = t('agency.testimonials.section_description');
  const linkText = t('agency.testimonials.section_description_link_text');

  return (
    <section className={styles.testimonials}>
      <div className="container">
        <Hero3
          label={t('agency.testimonials.section_label')}
          title={t('agency.testimonials.section_title')}
          description={
            <>
              {descriptionTemplate.split('{{link}}')[0]}
              <AnimatedLink href="https://g.page/r/CdYePvCaFA87EBM/review" external>
                {linkText}
              </AnimatedLink>
              {descriptionTemplate.split('{{link}}')[1]}
            </>
          }
          buttonText={t('agency.testimonials.view_all_button')}
          buttonLink="https://g.page/r/CdYePvCaFA87EAE/review"
        />
            <motion.div
              className={styles.carouselSection}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.7 }}
            >
                <TestimonialCarousel
                  testimonials={testimonialsData}
                  locale={locale}
                />
            </motion.div>
      </div>
    </section>
  );
}
