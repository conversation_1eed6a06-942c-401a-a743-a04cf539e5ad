'use client';
import { motion } from 'framer-motion';
import TextReveal from '@/components/TextReveal';
import Rounded from '@/common/RoundedButton';
import Separator from '@/components/Separator';
import styles from './style.module.scss';
    import GSAPTextReveal from '@/components/GSAPTextReveal';

    import { getPreset } from '@/components/GSAPTextReveal/presets';

export default function Hero3({
  label,
  title,
  description,
  showDescription = true,
  buttonText,
  buttonLink,
  className = '',
  labelDelay = 0.3,
  descriptionDelay = 0.4,
  buttonDelay = 0.5
}) {
  return (
    <div>
                <Separator
                  animated={true}
                  color="#000"
                  thickness={1}
                  className={styles.separator}
                />
                
      <div className={`${styles.header} ${className}`}>
        <div className={styles.labelSection}>
                    <GSAPTextReveal
        className={`text-big ${styles.sectionLabel}`}
          as="p"
          {...getPreset('lines', { delay: 0.2, stagger: 0.2 })}
        >
            {label}
          </GSAPTextReveal>
      </div>

      <div className={styles.contentSection}>
                        <GSAPTextReveal
                          className={styles.sectionTitle}
                          as="h2"
                          {...getPreset('hero')}
                        >
                          {title}
                        </GSAPTextReveal>
        
        {description && showDescription && (
        <GSAPTextReveal
        className={styles.description}
          as="div"
          {...getPreset('lines', { delay: 0.4, stagger: 0.2 })}
        >
          {description}
        </GSAPTextReveal>
        )}

        {buttonText && buttonLink && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, ease: 'easeInOut', delay: buttonDelay }}
          >
            <Rounded href={buttonLink} className={styles.viewAllButton}>
              <p>{buttonText}</p>
            </Rounded>
          </motion.div>
        )}
      </div>
    </div>
    </div>
  );
}
