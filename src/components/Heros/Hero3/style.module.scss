.separator {
  margin-bottom: var(--gap-padding);
}

.header {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--gap-padding);
  margin-bottom: calc(var(--gap-padding) * 3);
  align-items: start;

  .labelSection {
    .sectionLabel {
      margin: 0;
    }
  }

  .contentSection {
    .sectionTitle {
      margin: 0 0 24px 0;
      color: #1c1d20;
      max-width: 650px;
    }

    .description {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.6;
      color: rgba(28, 29, 32, 0.8);
      margin: 16px 0 24px 0;
      max-width: 600px;
    }

    .viewAllButton {
      margin-top: var(--gap-padding);
      width: fit-content;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .header {
    grid-template-columns: 1fr;
    gap: calc(var(--gap-padding) / 2);
    margin-bottom: calc(var(--gap-padding) * 2);

    .labelSection {
      margin-bottom: calc(var(--gap-padding) / 2);

    }

    .contentSection {
      .sectionTitle {
        margin-bottom: 16px;
      }

      .description {
        font-size: 15px;
        margin: 12px 0 20px 0;
      }

      .viewAllButton {
        margin-top: calc(var(--gap-padding) / 2);
      }
    }
  }
}

@media (max-width: 480px) {
  .header {
    .contentSection {
      .description {
        font-size: 14px;
        margin: 10px 0 16px 0;
      }
    }
  }
}
