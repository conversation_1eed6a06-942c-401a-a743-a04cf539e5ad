"use client";

import styles from './style.module.scss';
import { useInView, motion } from 'framer-motion';
import { useRef } from 'react';
import { slideUp, opacity } from './animation';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import CursorFollower, { useCursorFollower } from '@/components/CursorFollower';

export default function ExpertiseAccordion({
    title = "Nos expertises",
    expertises = []
}) {
    const container = useRef(null);
    const isInView = useInView(container, { once: true });

    // Hook pour gérer la bulle qui suit la souris
    const { isVisible, showCursor, hideCursor, updatePosition, cursorRef } = useCursorFollower();

    // Protection : s'assurer que expertises est un tableau
    const expertisesList = Array.isArray(expertises) ? expertises : [];

    // Gestionnaire de survol pour l'accordéon
    const handleMouseEnter = (e) => {
        showCursor(e.clientX, e.clientY);
    };

    const handleMouseMove = (e) => {
        updatePosition(e.clientX, e.clientY);
    };

    const handleMouseLeave = () => {
        hideCursor();
    };

    return (
        <div ref={container} className={`${styles.expertiseAccordion} container medium`}>
            <div className={styles.body}>
                {/* Titre à gauche */}
                    <h2 className={styles.titleSection}>
                     Nos expertises
                    </h2>

                {/* Accordéon à droite */}
                <motion.div
                    variants={opacity}
                    animate={isInView ? "open" : "closed"}
                    className={styles.accordionSection}
                    onMouseEnter={handleMouseEnter}
                    onMouseMove={handleMouseMove}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className={styles.accordionWrapper}>
                        <Accordion>
                            {expertisesList.map((expertise, index) => (
                                <AccordionItem
                                    key={index}
                                    hideIcon={true}
                                    title={expertise.title}
                                    items={expertise.items || []}
                                />
                            ))}
                        </Accordion>
                    </div>
                </motion.div>

                {/* Bulle qui suit la souris */}
                <CursorFollower
                    ref={cursorRef}
                    text="Découvrir"
                    backgroundColor="#FF413D"
                    textColor="white"
                    size={80}
                    fontSize={14}
                    fontWeight={300}
                    isVisible={isVisible}
                />
            </div>
        </div>
    );
}
