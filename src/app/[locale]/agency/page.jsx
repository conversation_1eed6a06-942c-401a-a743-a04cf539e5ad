import { getTranslation } from "@/hooks/useTranslation";
import AgencyClient from './AgencyClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'pages');

  return {
    title: t('agency.title'),
    description: t('agency.description'),
    openGraph: {
      title: t('agency.title'),
      description: t('agency.description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/agency`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('agency.title'),
      description: t('agency.description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function Agency({ params }) {
  return <AgencyClient params={params} />;
}
